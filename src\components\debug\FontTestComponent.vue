<template>
  <div class="font-test-container">
    <h2>字体渲染测试</h2>
    
    <div class="test-section">
      <h3>静态数字测试</h3>
      <div class="test-content static-content">
        <p>数字测试: 0123456789</p>
        <p>混合内容: 我有 64 个钻石和 32 个金锭</p>
        <p>Markdown数字: **123** 和 *456*</p>
      </div>
    </div>
    
    <div class="test-section">
      <h3>动态渲染测试</h3>
      <div class="test-content dynamic-content" v-html="dynamicContent"></div>
      <button @click="updateDynamicContent" class="test-button">更新动态内容</button>
      <button @click="forceRerender" class="test-button">强制重新渲染</button>
    </div>
    
    <div class="test-section">
      <h3>流式更新测试</h3>
      <div class="test-content streaming-content" v-html="streamingContent"></div>
      <button @click="startStreaming" class="test-button" :disabled="isStreaming">开始流式更新</button>
      <button @click="stopStreaming" class="test-button" :disabled="!isStreaming">停止流式更新</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue';
import { marked } from 'marked';

const dynamicContent = ref('');
const streamingContent = ref('');
const isStreaming = ref(false);
let streamingInterval: number | null = null;

const testTexts = [
  '这是第一段测试文本，包含数字 123 和 456',
  '**粗体数字**: 789, *斜体数字*: 101112',
  '合成配方需要 8 个圆石和 1 个红石',
  '```\n数字表格:\n1 | 2 | 3\n4 | 5 | 6\n```',
  '最终测试: 0123456789 全部数字'
];

const updateDynamicContent = () => {
  const randomText = testTexts[Math.floor(Math.random() * testTexts.length)];
  const markdown = `### 动态更新 ${Date.now()}\n\n${randomText}\n\n当前时间: ${new Date().toLocaleTimeString()}`;
  dynamicContent.value = marked.parse(markdown) as string;
};

const forceRerender = () => {
  nextTick(() => {
    const elements = document.querySelectorAll('.dynamic-content, .streaming-content');
    elements.forEach((element) => {
      const htmlElement = element as HTMLElement;
      if (htmlElement) {
        htmlElement.style.display = 'none';
        htmlElement.offsetHeight; // 强制重排
        htmlElement.style.display = '';
      }
    });
  });
};

const startStreaming = () => {
  if (isStreaming.value) return;
  
  isStreaming.value = true;
  streamingContent.value = '';
  
  const fullText = '这是一个模拟SSE流式更新的测试，包含数字 123456789 和各种字符。我们正在测试字体渲染是否正常工作。';
  let currentIndex = 0;
  
  streamingInterval = setInterval(() => {
    if (currentIndex < fullText.length) {
      streamingContent.value += fullText[currentIndex];
      currentIndex++;
    } else {
      stopStreaming();
      // 模拟SSE流结束后的重新渲染
      setTimeout(() => {
        forceRerender();
      }, 100);
    }
  }, 50) as unknown as number;
};

const stopStreaming = () => {
  if (streamingInterval) {
    clearInterval(streamingInterval);
    streamingInterval = null;
  }
  isStreaming.value = false;
};

// 初始化动态内容
updateDynamicContent();
</script>

<style scoped>
.font-test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  color: #ffffff;
}

.font-test-container h2 {
  color: #55ff55;
  text-align: center;
  margin-bottom: 30px;
  font-family: 'Minecraft', monospace;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid #55ff55;
}

.test-section h3 {
  color: #55ff55;
  margin-bottom: 15px;
  font-family: 'Minecraft', monospace;
}

.test-content {
  background: rgba(0, 0, 0, 0.3);
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
  font-family: 'Minecraft', 'Consolas', 'Monaco', 'Courier New', monospace;
  color: #ffffff;
  min-height: 60px;
  /* 强制字体重新计算和渲染 */
  font-variant-numeric: normal;
  font-feature-settings: normal;
  text-rendering: optimizeLegibility;
}

.test-content :deep(*) {
  font-family: 'Minecraft', 'Consolas', 'Monaco', 'Courier New', monospace !important;
  font-variant-numeric: normal !important;
  font-feature-settings: normal !important;
}

.test-button {
  background: #55ff55;
  color: #000;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 10px;
  font-family: 'Minecraft', monospace;
  transition: all 0.2s;
}

.test-button:hover:not(:disabled) {
  background: #7dff7d;
  transform: translateY(-1px);
}

.test-button:disabled {
  background: #666;
  color: #999;
  cursor: not-allowed;
}

.streaming-content {
  border-left: 3px solid #55ff55;
  padding-left: 15px;
}
</style>
